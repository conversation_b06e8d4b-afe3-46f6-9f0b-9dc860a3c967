# 扣子对话调试工具

一个简单易用的扣子(Coze) API调试工具，使用PHP + HTML实现。

## 功能特点

- 🎨 现代化UI设计，简约时尚
- 🔧 简单配置，即开即用
- 💬 支持单轮和多轮对话
- 🚀 实时对话测试
- 📱 响应式设计，支持移动端
- 🛡️ 安全的API调用处理

## 文件说明

- `coze_chat.html` - 前端界面文件
- `coze_api.php` - 后端API处理文件
- `README.md` - 使用说明文档

## 使用方法

### 1. 环境要求

- PHP 7.0 或更高版本
- 支持cURL扩展
- Web服务器（Apache/Nginx等）

### 2. 部署步骤

1. 将所有文件上传到Web服务器目录
2. 确保PHP有读写权限
3. 访问 `coze_chat.html` 开始使用

### 3. 配置说明

在界面中需要配置以下信息：

- **API Token**: 您的扣子API访问令牌
- **Bot ID**: 要调用的机器人ID
- **用户ID**: 对话用户的唯一标识
- **对话模式**: 
  - 单轮对话(0): 每次都是独立的对话
  - 多轮对话(1): 保持对话上下文

### 4. 获取API信息

1. 访问 [扣子开放平台](https://www.coze.cn/open)
2. 创建应用并获取API Token
3. 创建或选择要使用的Bot，获取Bot ID

## API接口说明

### 请求格式

```json
{
    "message": "用户消息内容",
    "api_token": "您的API Token",
    "bot_id": "机器人ID", 
    "user_id": "用户ID",
    "chat_mode": 0,
    "conversation_id": "对话ID（多轮对话时使用）"
}
```

### 响应格式

成功响应：
```json
{
    "success": true,
    "response": "AI回复内容",
    "conversation_id": "对话ID"
}
```

错误响应：
```json
{
    "success": false,
    "error": "错误信息"
}
```

## 自定义配置

### 修改样式

可以编辑 `coze_chat.html` 中的CSS样式来自定义界面外观。

### 添加日志功能

在 `coze_api.php` 中取消注释日志相关代码：

```php
// 取消注释这行来启用日志
file_put_contents('coze_chat.log', json_encode($log_data) . "\n", FILE_APPEND);
```

### 安全设置

1. 建议在生产环境中添加访问控制
2. 可以添加请求频率限制
3. 考虑使用HTTPS协议

## 故障排除

### 常见问题

1. **无法连接API**
   - 检查网络连接
   - 验证API Token是否正确
   - 确认Bot ID是否有效

2. **PHP错误**
   - 检查PHP版本是否支持
   - 确认cURL扩展已启用
   - 查看服务器错误日志

3. **跨域问题**
   - 确保PHP文件设置了正确的CORS头
   - 检查浏览器控制台错误信息

### 调试模式

可以在浏览器开发者工具中查看网络请求和响应，帮助定位问题。

## 技术支持

如有问题，请检查：
1. 扣子官方文档：https://www.coze.cn/open/docs
2. PHP错误日志
3. 浏览器控制台错误信息

## 更新日志

- v1.0.0 - 初始版本，支持基本对话功能

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扣子对话调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .config-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .config-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .config-row label {
            width: 120px;
            font-weight: bold;
            color: #333;
        }
        
        .config-row input, .config-row select {
            flex: 1;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .config-row input:focus, .config-row select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .chat-container {
            height: 400px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.assistant {
            justify-content: flex-start;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e1e5e9;
            color: #333;
        }
        
        .input-section {
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .message-input:focus {
            border-color: #4facfe;
        }
        
        .send-btn {
            padding: 12px 24px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        .send-btn:hover {
            transform: translateY(-2px);
        }
        
        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }
        
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #e74c3c;
        }
        
        .clear-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 扣子对话调试工具</h1>
            <p>简单易用的扣子API测试界面</p>
        </div>
        
        <div class="config-section">
            <div class="config-row">
                <label>API Token:</label>
                <input type="password" id="apiToken" placeholder="请输入您的扣子API Token" />
            </div>
            <div class="config-row">
                <label>Bot ID:</label>
                <input type="text" id="botId" placeholder="请输入Bot ID" />
            </div>
            <div class="config-row">
                <label>用户ID:</label>
                <input type="text" id="userId" placeholder="请输入用户ID" value="test_user_123" />
            </div>
            <div class="config-row">
                <label>对话模式:</label>
                <select id="chatMode">
                    <option value="0">单轮对话</option>
                    <option value="1">多轮对话</option>
                </select>
                <button class="clear-btn" onclick="clearChat()">清空对话</button>
            </div>
        </div>
        
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <div class="message-content">
                        👋 您好！我是扣子AI助手，请配置好API信息后开始对话。
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div>🤔 AI正在思考中...</div>
            </div>
        </div>
        
        <div class="input-section">
            <input type="text" class="message-input" id="messageInput" placeholder="请输入您的消息..." onkeypress="handleKeyPress(event)" />
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
            <button class="send-btn" style="background: #6c757d; margin-left: 5px;" onclick="debugMessage()">调试</button>
        </div>
    </div>

    <script>
        let conversationId = null;
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function addMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function showError(message) {
            const messagesContainer = document.getElementById('chatMessages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = '❌ ' + message;
            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function setLoading(isLoading) {
            document.getElementById('loading').style.display = isLoading ? 'block' : 'none';
            document.getElementById('sendBtn').disabled = isLoading;
        }
        
        function clearChat() {
            document.getElementById('chatMessages').innerHTML = `
                <div class="message assistant">
                    <div class="message-content">
                        👋 对话已清空，请重新开始对话。
                    </div>
                </div>
            `;
            conversationId = null;
        }
        
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) return;

            const apiToken = document.getElementById('apiToken').value.trim();
            const botId = document.getElementById('botId').value.trim();
            const userId = document.getElementById('userId').value.trim();
            const chatMode = document.getElementById('chatMode').value;

            if (!apiToken || !botId || !userId) {
                showError('请填写完整的API配置信息');
                return;
            }

            // 添加用户消息
            addMessage(message, true);
            messageInput.value = '';

            setLoading(true);

            try {
                const response = await fetch('coze_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        api_token: apiToken,
                        bot_id: botId,
                        user_id: userId,
                        chat_mode: chatMode,
                        conversation_id: conversationId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    addMessage(data.response);
                    if (data.conversation_id) {
                        conversationId = data.conversation_id;
                    }
                } else {
                    showError(data.error || '请求失败');
                }
            } catch (error) {
                showError('网络请求失败: ' + error.message);
            }

            setLoading(false);
        }

        async function debugMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) {
                showError('请输入要调试的消息');
                return;
            }

            const apiToken = document.getElementById('apiToken').value.trim();
            const botId = document.getElementById('botId').value.trim();
            const userId = document.getElementById('userId').value.trim();
            const chatMode = document.getElementById('chatMode').value;

            if (!apiToken || !botId || !userId) {
                showError('请填写完整的API配置信息');
                return;
            }

            // 添加用户消息
            addMessage(message + ' [调试模式]', true);
            messageInput.value = '';

            setLoading(true);

            try {
                const response = await fetch('debug_response.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        api_token: apiToken,
                        bot_id: botId,
                        user_id: userId,
                        chat_mode: chatMode,
                        conversation_id: conversationId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 显示调试信息
                    const debugInfo = `
🔍 调试信息:
📤 请求数据: ${JSON.stringify(data.request_data, null, 2)}
📥 原始响应: ${data.raw_response}
📊 解析后响应: ${JSON.stringify(data.parsed_response, null, 2)}
🌐 HTTP状态码: ${data.http_code}
                    `;
                    addMessage(debugInfo);
                } else {
                    const errorInfo = `
❌ 调试错误信息:
📤 请求数据: ${JSON.stringify(data.request_data || {}, null, 2)}
📥 原始响应: ${data.raw_response || '无'}
❌ 错误: ${data.error}
                    `;
                    addMessage(errorInfo);
                }
            } catch (error) {
                showError('调试请求失败: ' + error.message);
            }

            setLoading(false);
        }
    </script>
</body>
</html>

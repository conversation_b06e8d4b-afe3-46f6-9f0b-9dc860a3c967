<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扣子API快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 扣子API快速测试</h1>
        <p>使用您提供的配置信息进行快速测试</p>
        
        <div>
            <label>测试消息:</label>
            <input type="text" id="testMessage" value="你好，请介绍一下自己" placeholder="输入要测试的消息">
        </div>
        
        <button class="btn" onclick="testNormal()">正常测试</button>
        <button class="btn" onclick="testDebug()">调试模式</button>
        <button class="btn" onclick="clearResult()">清空结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_TOKEN = 'pat_c4KLLmWsJnA5cpHAuZmYQyP1RqatrbVc2VNncASJ49TsWG1RyvBVkS2BWccWkBLq';
        const BOT_ID = '7519724252523347995';
        const USER_ID = 'test_user_123';

        function showResult(content, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.textContent = content;
        }

        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }

        async function testNormal() {
            const message = document.getElementById('testMessage').value.trim();
            if (!message) {
                showResult('请输入测试消息', true);
                return;
            }

            showResult('正在测试...');

            try {
                const response = await fetch('coze_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        api_token: API_TOKEN,
                        bot_id: BOT_ID,
                        user_id: USER_ID,
                        chat_mode: 0
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showResult(`✅ 测试成功！\n\n用户: ${message}\nAI回复: ${data.response}`);
                } else {
                    showResult(`❌ 测试失败！\n\n错误信息: ${data.error}`, true);
                }
            } catch (error) {
                showResult(`❌ 网络错误！\n\n${error.message}`, true);
            }
        }

        async function testDebug() {
            const message = document.getElementById('testMessage').value.trim();
            if (!message) {
                showResult('请输入测试消息', true);
                return;
            }

            showResult('正在调试...');

            try {
                const response = await fetch('debug_response.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        api_token: API_TOKEN,
                        bot_id: BOT_ID,
                        user_id: USER_ID,
                        chat_mode: 0
                    })
                });

                const data = await response.json();

                if (data.success) {
                    const debugInfo = `🔍 调试信息:

📤 请求数据:
${JSON.stringify(data.request_data, null, 2)}

📥 原始响应:
${data.raw_response}

📊 解析后响应:
${JSON.stringify(data.parsed_response, null, 2)}

🌐 HTTP状态码: ${data.http_code}`;
                    showResult(debugInfo);
                } else {
                    const errorInfo = `❌ 调试错误:

📤 请求数据:
${JSON.stringify(data.request_data || {}, null, 2)}

📥 原始响应:
${data.raw_response || '无'}

❌ 错误: ${data.error}`;
                    showResult(errorInfo, true);
                }
            } catch (error) {
                showResult(`❌ 调试请求失败！\n\n${error.message}`, true);
            }
        }
    </script>
</body>
</html>

<?php
/**
 * 扣子API响应调试工具
 * 用于查看API的原始响应内容
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => '只允许POST请求']);
    exit;
}

// 获取POST数据
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['success' => false, 'error' => '无效的JSON数据']);
    exit;
}

// 验证必需参数
$required_fields = ['message', 'api_token', 'bot_id', 'user_id'];
foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        echo json_encode(['success' => false, 'error' => "缺少必需参数: {$field}"]);
        exit;
    }
}

// 提取参数
$message = $data['message'];
$api_token = $data['api_token'];
$bot_id = $data['bot_id'];
$user_id = $data['user_id'];
$chat_mode = isset($data['chat_mode']) ? intval($data['chat_mode']) : 0;
$conversation_id = isset($data['conversation_id']) ? $data['conversation_id'] : null;

/**
 * 调用扣子Chat API并返回原始响应
 */
function callCozeAPIDebug($api_token, $bot_id, $user_id, $message, $chat_mode, $conversation_id = null) {
    $url = 'https://api.coze.cn/v3/chat';
    
    // 构建请求数据
    $request_data = [
        'bot_id' => $bot_id,
        'user_id' => $user_id,
        'stream' => false,
        'auto_save_history' => true,
        'additional_messages' => [
            [
                'role' => 'user',
                'content' => $message,
                'content_type' => 'text'
            ]
        ]
    ];
    
    // 如果是多轮对话且有conversation_id，则添加
    if ($chat_mode == 1 && $conversation_id) {
        $request_data['conversation_id'] = $conversation_id;
    }
    
    // 设置请求头
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_token
    ];
    
    // 初始化cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($request_data),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return [
            'success' => false, 
            'error' => 'cURL错误: ' . $curl_error,
            'request_data' => $request_data,
            'headers' => $headers
        ];
    }
    
    if ($http_code !== 200) {
        return [
            'success' => false, 
            'error' => "HTTP错误: {$http_code}",
            'request_data' => $request_data,
            'headers' => $headers,
            'raw_response' => $response
        ];
    }
    
    $result = json_decode($response, true);
    
    return [
        'success' => true,
        'request_data' => $request_data,
        'headers' => $headers,
        'raw_response' => $response,
        'parsed_response' => $result,
        'http_code' => $http_code
    ];
}

try {
    // 调用扣子API
    $debug_result = callCozeAPIDebug($api_token, $bot_id, $user_id, $message, $chat_mode, $conversation_id);
    
    // 返回完整的调试信息
    echo json_encode($debug_result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '服务器错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>

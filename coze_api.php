<?php
/**
 * 扣子对话API调试工具
 * 简单的PHP后端处理扣子API调用
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => '只允许POST请求']);
    exit;
}

// 获取POST数据
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['success' => false, 'error' => '无效的JSON数据']);
    exit;
}

// 验证必需参数
$required_fields = ['message', 'api_token', 'bot_id', 'user_id'];
foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        echo json_encode(['success' => false, 'error' => "缺少必需参数: {$field}"]);
        exit;
    }
}

// 提取参数
$message = $data['message'];
$api_token = $data['api_token'];
$bot_id = $data['bot_id'];
$user_id = $data['user_id'];
$chat_mode = isset($data['chat_mode']) ? intval($data['chat_mode']) : 0;
$conversation_id = isset($data['conversation_id']) ? $data['conversation_id'] : null;

/**
 * 调用扣子Chat API
 */
function callCozeAPI($api_token, $bot_id, $user_id, $message, $chat_mode, $conversation_id = null) {
    $url = 'https://api.coze.cn/v3/chat';
    
    // 构建请求数据
    $request_data = [
        'bot_id' => $bot_id,
        'user_id' => $user_id,
        'stream' => false,
        'auto_save_history' => true,
        'additional_messages' => [
            [
                'role' => 'user',
                'content' => $message,
                'content_type' => 'text'
            ]
        ]
    ];
    
    // 如果是多轮对话且有conversation_id，则添加
    if ($chat_mode == 1 && $conversation_id) {
        $request_data['conversation_id'] = $conversation_id;
    }
    
    // 设置请求头
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_token
    ];
    
    // 初始化cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($request_data),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return ['success' => false, 'error' => 'cURL错误: ' . $curl_error];
    }
    
    if ($http_code !== 200) {
        return ['success' => false, 'error' => "HTTP错误: {$http_code}"];
    }
    
    $result = json_decode($response, true);
    
    if (!$result) {
        return ['success' => false, 'error' => '无法解析API响应'];
    }
    
    return ['success' => true, 'data' => $result];
}

/**
 * 从API响应中提取消息内容
 */
function extractMessageFromResponse($api_response) {
    if (!isset($api_response['data'])) {
        return '无法获取响应数据';
    }
    
    $data = $api_response['data'];
    
    // 检查是否有错误
    if (isset($data['code']) && $data['code'] !== 0) {
        $error_msg = isset($data['msg']) ? $data['msg'] : '未知错误';
        return "API错误 ({$data['code']}): {$error_msg}";
    }
    
    // 提取消息内容
    if (isset($data['messages']) && is_array($data['messages'])) {
        $messages = [];
        foreach ($data['messages'] as $msg) {
            if (isset($msg['role']) && $msg['role'] === 'assistant' && isset($msg['content'])) {
                $messages[] = $msg['content'];
            }
        }
        
        if (!empty($messages)) {
            return implode("\n", $messages);
        }
    }
    
    // 备用方案：检查其他可能的响应格式
    if (isset($data['content'])) {
        return $data['content'];
    }
    
    if (isset($data['answer'])) {
        return $data['answer'];
    }
    
    return '收到响应但无法提取消息内容';
}

/**
 * 记录日志（可选）
 */
function logRequest($user_id, $message, $response) {
    $log_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $user_id,
        'message' => $message,
        'response' => $response
    ];
    
    // 可以选择将日志写入文件
    // file_put_contents('coze_chat.log', json_encode($log_data) . "\n", FILE_APPEND);
}

try {
    // 调用扣子API
    $api_result = callCozeAPI($api_token, $bot_id, $user_id, $message, $chat_mode, $conversation_id);
    
    if (!$api_result['success']) {
        echo json_encode([
            'success' => false,
            'error' => $api_result['error']
        ]);
        exit;
    }
    
    // 提取响应消息
    $response_message = extractMessageFromResponse($api_result);
    
    // 提取conversation_id（用于多轮对话）
    $new_conversation_id = null;
    if (isset($api_result['data']['conversation_id'])) {
        $new_conversation_id = $api_result['data']['conversation_id'];
    }
    
    // 记录日志（可选）
    logRequest($user_id, $message, $response_message);
    
    // 返回成功响应
    $response = [
        'success' => true,
        'response' => $response_message
    ];
    
    if ($new_conversation_id) {
        $response['conversation_id'] = $new_conversation_id;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '服务器错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
